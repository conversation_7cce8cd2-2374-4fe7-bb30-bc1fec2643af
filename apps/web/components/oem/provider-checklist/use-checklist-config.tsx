import type { ExtendedWarrantyRequest } from "@/types/warranty";
import { QuoteStatus, type StripeConnection } from "@rvhelp/database";
import type { ReactNode } from "react";
import type { QuoteWithMessages } from "@/app/(main)/provider/(dashboard)/jobs/[id]/types";
import { WarrantyRequestStatus } from "@rvhelp/database";
import { CheckCircle, Clock, XCircle } from "lucide-react";
import Button from "../../Button";

interface ProviderChecklistProps {
	quote: QuoteWithMessages;
	warrantyRequest: ExtendedWarrantyRequest;
	stripeConnect: StripeConnection;
	onStartJob: () => void;
	onRequestAuthorization: () => void;
	onConfigureStripe: () => void;
	onRequestPayment: () => void;
	onDownloadForms: () => void;
	onCompleteJob: () => void;
}

export function useChecklistConfig({
	quote,
	warrantyRequest,
	stripeConnect,
	onStartJob,
	onRequestAuthorization,
	onConfigureStripe,
	onRequestPayment,
	onDownloadForms,
	onCompleteJob
}: ProviderChecklistProps) {
	const getAuthorizationMessage = (warrantyRequest) => {
		if (warrantyRequest.status === "AUTHORIZATION_REJECTED") {
			const rejectedUpdate = warrantyRequest.timeline_updates?.find(
				(update) => update.event_type === "AUTHORIZATION_REJECTED"
			);
			return (
				<p>
					<p className="font-bold">
						Authorization rejected - Please contact OEM support for assistance.
					</p>
					{rejectedUpdate?.details?.notes && (
						<p className="pt-2 pl-2 italic">{`${rejectedUpdate?.details?.notes}.`}</p>
					)}
				</p>
			);
		}
		if (warrantyRequest.status === "AUTHORIZATION_FEEDBACK") {
			const feedbackUpdate = warrantyRequest.timeline_updates?.find(
				(update) => update.event_type === "AUTHORIZATION_FEEDBACK"
			);
			return (
				<p>
					<p className="font-bold">
						Feedback requested - Please contact OEM support for assistance.
					</p>
					{feedbackUpdate?.details?.notes && (
						<p className="pt-2 pl-2 italic">{`${feedbackUpdate?.details?.notes}.`}</p>
					)}
				</p>
			);
		}
		return "";
	};

	const checklistItems = [
		{
			id: "accept",
			title: "Customer Acceptance",
			status: {
				current: WarrantyRequestStatus.JOB_ACCEPTED,
				next: WarrantyRequestStatus.JOB_STARTED // || WarrantyRequestStatus.JOB_WITHDRAWN
			},
			completed:
				warrantyRequest.status === "INVOICE_PAID" ||
				quote.status === QuoteStatus.IN_PROGRESS,
			description: (
				<>
					<p>Once the customer accepts your quote, you can begin work.</p>
					<p>
						If you have contacted the customer and they have agreed to work with
						you, you can click here to update the status to 'In Progress' to
						begin work.
					</p>
				</>
			),
			success: "Job accepted",
			action: () => onStartJob()
		},
		{
			id: "forms",
			title: "OEM Requirements",
			status: {
				current: WarrantyRequestStatus.JOB_STARTED,
				next: WarrantyRequestStatus.JOB_STARTED,
			},
			completed: warrantyRequest.attachments_acknowledged,
			hidden:
				!warrantyRequest.requires_return &&
				!warrantyRequest.attachments?.some(
					(att) => att.required || att.completed
				),
			description:
				"Before your service call, view and sign off on the OEM's requirements for this job." as ReactNode,
			success: "Forms downloaded",
			action: onDownloadForms
		},
		{
			id: "diagnose",
			title: "Diagnosis and Estimate",
			status: {
				current: WarrantyRequestStatus.JOB_STARTED || WarrantyRequestStatus.AUTHORIZATION_APPROVED || WarrantyRequestStatus.PARTS_ORDERED || WarrantyRequestStatus.AUTHORIZATION_REQUESTED || WarrantyRequestStatus.AUTHORIZATION_FEEDBACK,
				next: WarrantyRequestStatus.AUTHORIZATION_APPROVED || WarrantyRequestStatus.PARTS_ORDERED || WarrantyRequestStatus.AUTHORIZATION_REJECTED || WarrantyRequestStatus.AUTHORIZATION_FEEDBACK
			},
			completed:
				warrantyRequest.status === "INVOICE_PAID" ||
				(warrantyRequest.status !== "AUTHORIZATION_REJECTED" &&
					warrantyRequest.status !== "AUTHORIZATION_FEEDBACK" &&
					warrantyRequest.cause !== "" &&
					warrantyRequest.correction !== "" &&
					warrantyRequest.estimated_hours > 0),
			// && (warrantyRequest.attachments?.some(
			// 	(att) => att.required && att.completed
			// ) ||
			// 	!warrantyRequest.attachments?.some((att) => att.required))
			description: (
				<>
					<p className="mb-3">
						After visiting the customer and performing your diagnosis, submit
						the cause, correction, and your estimate for the service.
					</p>

					<div className="bg-blue-50 border-l-4 border-blue-400 p-3 rounded-r-md mb-2">
						<div className="flex items-start">
							<div className="ml-2 w-full">
								<p className="text-sm font-bold text-blue-800 mb-3">
									Approvals
								</p>
								<div className="bg-white rounded-md border border-blue-200 overflow-hidden">
									<table className="w-full text-sm">
										<thead className="bg-gray-50 border-b border-gray-200">
											<tr>
												<th className="px-3 py-2 text-left font-medium text-gray-700">Status</th>
												<th className="px-3 py-2 text-left font-medium text-gray-700">Hours</th>
												<th className="px-3 py-2 text-left font-medium text-gray-700">Description</th>
											</tr>
										</thead>
										<tbody>
											<tr className="border-b border-gray-100 last:border-b-0">
												<td className="px-4 py-2 font-medium text-blue-900">
													✅
												</td>
												<td className="px-3 py-2 font-medium text-blue-900">
													{warrantyRequest.approved_hours}
												</td>
												<td className="px-3 py-2 text-gray-700">
													General Diagnostic
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>

					<p className="text-sm text-gray-600 mt-2">
						<strong>Need more time?</strong> If you require additional hours
						beyond the pre-approved amount, OEM authorization will be required.
						You can request additional authorization when submitting your
						diagnosis.
					</p>

					<div className="flex justify-end py-2 gap-3">
						<Button
							onClick={onRequestAuthorization}
							disabled={warrantyRequest.status === "AUTHORIZATION_REQUESTED"}
						>
							Update Estimate
						</Button>
						<Button
							onClick={onRequestAuthorization}
							disabled={warrantyRequest.status === "AUTHORIZATION_REQUESTED"}
						>
							Update Progress
						</Button>
						<Button
							onClick={onRequestAuthorization}
							disabled={warrantyRequest.status === "AUTHORIZATION_REQUESTED"}
						>
							Complete Job
						</Button>

					</div>
				</>
			),
			success: "Diagnosis and estimate submitted",
			action: () => onRequestAuthorization()
		},
		{
			id: "authorization",
			title: "Authorization",
			status: {
				current: WarrantyRequestStatus.AUTHORIZATION_REQUESTED || WarrantyRequestStatus.AUTHORIZATION_APPROVED || WarrantyRequestStatus.PARTS_ORDERED || WarrantyRequestStatus.AUTHORIZATION_FEEDBACK || WarrantyRequestStatus.AUTHORIZATION_REJECTED,
				next: WarrantyRequestStatus.AUTHORIZATION_REQUESTED || WarrantyRequestStatus.INVOICE_CREATED,
			},
			completed:
				warrantyRequest.status === "INVOICE_PAID" ||
				(warrantyRequest.estimated_hours &&
					warrantyRequest.approved_hours >= warrantyRequest.estimated_hours &&
					warrantyRequest.status === "AUTHORIZATION_APPROVED") ||
				(warrantyRequest.estimated_hours &&
					warrantyRequest.approved_hours >= warrantyRequest.estimated_hours &&
					warrantyRequest.status === "PARTS_ORDERED") ||
				warrantyRequest.status.startsWith("INVOICE_"),
			failed:
				warrantyRequest.status === "AUTHORIZATION_REJECTED" ||
				warrantyRequest.status === "AUTHORIZATION_FEEDBACK",
			warning: warrantyRequest.status === "AUTHORIZATION_FEEDBACK",
			description:
				"Await approval of estimate (if estimate is greater than pre-approval)" as ReactNode,
			failure: getAuthorizationMessage(warrantyRequest),
			success:
				warrantyRequest.status === "AUTHORIZATION_APPROVED"
					? "Authorization approved"
					: warrantyRequest.status === "PARTS_ORDERED"
						? "Authorization approved & parts ordered"
						: "No authorization required",
			action: () => { }
		},
		{
			id: "stripe",
			title: "Banking Information",
			status: {
				current: WarrantyRequestStatus.AUTHORIZATION_REQUESTED || WarrantyRequestStatus.AUTHORIZATION_APPROVED || WarrantyRequestStatus.PARTS_ORDERED || WarrantyRequestStatus.AUTHORIZATION_FEEDBACK || WarrantyRequestStatus.AUTHORIZATION_REJECTED,
				next: WarrantyRequestStatus.AUTHORIZATION_REQUESTED || WarrantyRequestStatus.INVOICE_CREATED,
			},
			completed: stripeConnect?.stripe_account_status === "active",
			description:
				"Configure your Stripe account to receive payments" as ReactNode,
			success: "Stripe account configured",
			action: onConfigureStripe
		},
		{
			id: "work",
			title: "Invoicing",
			status: {
				current: WarrantyRequestStatus.AUTHORIZATION_REQUESTED || WarrantyRequestStatus.AUTHORIZATION_APPROVED || WarrantyRequestStatus.AUTHORIZATION_FEEDBACK,
				next: WarrantyRequestStatus.AUTHORIZATION_REQUESTED || WarrantyRequestStatus.INVOICE_CREATED,
			},
			completed:
				warrantyRequest.status === "INVOICE_CREATED" ||
				warrantyRequest.status === "INVOICE_PAID",
			description:
				"After service is complete, create and submit an invoice to OEM to receive payment." as ReactNode,
			success: "Invoice submitted for payment",
			action: onRequestPayment
		},
		{
			id: "payment",
			title: "Payment",
			completed: warrantyRequest.status === "INVOICE_PAID",
			description: "Awaiting payment" as ReactNode,
			success: "Payment received",
			action: warrantyRequest.status === "INVOICE_PAID" ? onCompleteJob : () => { }
		}
	];

	return {
		checklistItems,
		currentItemIndex: checklistItems.findIndex(
			(item) => !item.completed && !item.hidden
		)
	};
}
